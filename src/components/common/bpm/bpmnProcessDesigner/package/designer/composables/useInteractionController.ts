import { ref, onBeforeUnmount, type Ref } from 'vue'

/**
 * BPMN交互控制器
 * 负责处理拖拽、缩放、触摸等交互功能
 */
export function useInteractionController(isMobileDevice: Ref<boolean>) {
  const defaultZoom = ref(1)
  let bpmnModeler: any = null
  let isDragging = false
  let lastX = 0
  let lastY = 0

  // 事件处理器引用，用于清理
  const eventHandlers = {
    mousemove: null as ((event: MouseEvent) => void) | null,
    mouseup: null as (() => void) | null,
    touchmove: null as ((event: TouchEvent) => void) | null,
    touchend: null as (() => void) | null,
  }

  /**
   * 设置BPMN建模器实例
   */
  const setBpmnModeler = (modeler: any) => {
    bpmnModeler = modeler
  }

  /**
   * 初始化默认缩放比例
   */
  const initDefaultZoom = () => {
    if (isMobileDevice.value) {
      defaultZoom.value = 0.5
    } else {
      defaultZoom.value = 1
    }
  }

  /**
   * 放大操作
   * @param zoomStep 缩放步长，默认0.1
   */
  const processZoomIn = (zoomStep = 0.1) => {
    if (!bpmnModeler) return

    const newZoom = Math.floor(defaultZoom.value * 100 + zoomStep * 100) / 100
    if (newZoom > 4) {
      console.warn('[Process Viewer Warn]: The zoom ratio cannot be greater than 4')
      return
    }
    defaultZoom.value = newZoom
    bpmnModeler.get('canvas').zoom(defaultZoom.value)
  }

  /**
   * 缩小操作
   * @param zoomStep 缩放步长，默认0.1
   */
  const processZoomOut = (zoomStep = 0.1) => {
    if (!bpmnModeler) return

    const newZoom = Math.floor(defaultZoom.value * 100 - zoomStep * 100) / 100
    if (newZoom < 0.2) {
      console.warn('[Process Viewer Warn]: The zoom ratio cannot be less than 0.2')
      return
    }
    defaultZoom.value = newZoom
    bpmnModeler.get('canvas').zoom(defaultZoom.value)
  }

  /**
   * 重置缩放
   */
  const processReZoom = () => {
    if (!bpmnModeler) return

    if (isMobileDevice.value) {
      bpmnModeler.get('canvas').zoom(0.5)
      defaultZoom.value = 0.5
    } else {
      bpmnModeler.get('canvas').zoom(1)
      defaultZoom.value = 1
    }
  }

  /**
   * 适应视口缩放
   */
  const fitViewport = () => {
    if (!bpmnModeler) return
    
    const canvas = bpmnModeler.get('canvas')
    canvas.zoom('fit-viewport', 'auto')
    
    // 更新缩放值
    const viewbox = canvas.viewbox()
    defaultZoom.value = viewbox.scale
  }

  /**
   * 处理移动事件（鼠标或触摸）
   */
  const handleMove = (clientX: number, clientY: number) => {
    if (isDragging && bpmnModeler) {
      const dx = clientX - lastX
      const dy = clientY - lastY
      const canvas = bpmnModeler.get('canvas')
      canvas.scroll({ dx, dy })
      lastX = clientX
      lastY = clientY
    }
  }

  /**
   * 处理移动结束事件
   */
  const handleEnd = () => {
    isDragging = false
  }

  /**
   * 处理移动开始事件
   */
  const handleStart = (clientX: number, clientY: number) => {
    isDragging = true
    lastX = clientX
    lastY = clientY
  }

  /**
   * 初始化拖拽功能
   */
  const initDragging = () => {
    if (!bpmnModeler) return

    const eventBus = bpmnModeler.get('eventBus')

    // 监听元素鼠标按下事件
    eventBus.on('element.mousedown', (event: any) => {
      handleStart(event.originalEvent.clientX, event.originalEvent.clientY)
    })

    // 创建鼠标移动处理器
    const mouseMoveHandler = (event: MouseEvent) => {
      handleMove(event.clientX, event.clientY)
    }

    // 创建鼠标释放处理器
    const mouseUpHandler = () => {
      handleEnd()
    }

    // 创建触摸移动处理器
    const touchMoveHandler = (event: TouchEvent) => {
      if (event.touches.length === 1) {
        const touch = event.touches[0]
        handleMove(touch.clientX, touch.clientY)
        event.preventDefault() // 防止页面滚动
      }
    }

    // 创建触摸结束处理器
    const touchEndHandler = () => {
      handleEnd()
    }

    // 添加事件监听器
    document.addEventListener('mousemove', mouseMoveHandler)
    document.addEventListener('mouseup', mouseUpHandler)

    // 移动端触摸事件
    if (isMobileDevice.value) {
      document.addEventListener('touchmove', touchMoveHandler, { passive: false })
      document.addEventListener('touchend', touchEndHandler)
      
      // 保存触摸事件处理器引用
      eventHandlers.touchmove = touchMoveHandler
      eventHandlers.touchend = touchEndHandler
    }

    // 保存鼠标事件处理器引用
    eventHandlers.mousemove = mouseMoveHandler
    eventHandlers.mouseup = mouseUpHandler
  }

  /**
   * 初始化触摸缩放功能（移动端）
   */
  const initTouchZoom = () => {
    if (!bpmnModeler || !isMobileDevice.value) return

    const canvas = bpmnModeler.get('canvas')
    let initialDistance = 0
    let initialZoom = defaultZoom.value

    const getTouchDistance = (touches: TouchList) => {
      if (touches.length < 2) return 0
      const touch1 = touches[0]
      const touch2 = touches[1]
      const dx = touch1.clientX - touch2.clientX
      const dy = touch1.clientY - touch2.clientY
      return Math.sqrt(dx * dx + dy * dy)
    }

    const handleTouchStart = (event: TouchEvent) => {
      if (event.touches.length === 2) {
        initialDistance = getTouchDistance(event.touches)
        initialZoom = defaultZoom.value
        event.preventDefault()
      }
    }

    const handleTouchMove = (event: TouchEvent) => {
      if (event.touches.length === 2 && initialDistance > 0) {
        const currentDistance = getTouchDistance(event.touches)
        const scale = currentDistance / initialDistance
        const newZoom = Math.max(0.2, Math.min(4, initialZoom * scale))
        
        defaultZoom.value = newZoom
        canvas.zoom(newZoom)
        event.preventDefault()
      }
    }

    const handleTouchEnd = (event: TouchEvent) => {
      if (event.touches.length < 2) {
        initialDistance = 0
      }
    }

    // 添加触摸缩放事件监听器
    const canvasElement = canvas.getContainer()
    canvasElement.addEventListener('touchstart', handleTouchStart, { passive: false })
    canvasElement.addEventListener('touchmove', handleTouchMove, { passive: false })
    canvasElement.addEventListener('touchend', handleTouchEnd, { passive: false })

    // 保存事件处理器以便清理
    onBeforeUnmount(() => {
      canvasElement.removeEventListener('touchstart', handleTouchStart)
      canvasElement.removeEventListener('touchmove', handleTouchMove)
      canvasElement.removeEventListener('touchend', handleTouchEnd)
    })
  }

  /**
   * 清理事件监听器
   */
  const cleanup = () => {
    // 清理鼠标事件
    if (eventHandlers.mousemove) {
      document.removeEventListener('mousemove', eventHandlers.mousemove)
    }
    if (eventHandlers.mouseup) {
      document.removeEventListener('mouseup', eventHandlers.mouseup)
    }

    // 清理触摸事件
    if (eventHandlers.touchmove) {
      document.removeEventListener('touchmove', eventHandlers.touchmove)
    }
    if (eventHandlers.touchend) {
      document.removeEventListener('touchend', eventHandlers.touchend)
    }

    // 重置状态
    isDragging = false
    bpmnModeler = null
  }

  /**
   * 下载流程图
   */
  const downloadDiagram = () => {
    if (!bpmnModeler) return

    bpmnModeler.saveSVG((err: any, svg: string) => {
      if (err) {
        console.error('导出 SVG 失败', err)
        return
      }

      const name = '流程图_' + new Date().getTime()
      const blob = new Blob([svg], { type: 'image/svg+xml;charset=UTF-8' })
      const link = document.createElement('a')
      link.href = URL.createObjectURL(blob)
      link.download = name + '.svg'
      document.body.appendChild(link)
      
      // 清理
      document.body.removeChild(link)

      // 将 SVG 转换为 PNG 并下载
      convertSvgToPng(svg, name)
    })
  }

  /**
   * SVG 转换为 PNG
   */
  const convertSvgToPng = (svgData: string, fileName: string) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()

    img.onload = () => {
      canvas.width = img.width
      canvas.height = img.height
      ctx?.drawImage(img, 0, 0)

      try {
        const pngUrl = canvas.toDataURL('image/png')
        const link = document.createElement('a')
        link.href = pngUrl
        link.download = fileName + '.png'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      } catch (e) {
        console.error('PNG 导出失败', e)
      }
    }

    const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' })
    const url = URL.createObjectURL(svgBlob)
    img.src = url
  }

  // 组件卸载时清理
  onBeforeUnmount(() => {
    cleanup()
  })

  return {
    defaultZoom,
    setBpmnModeler,
    initDefaultZoom,
    processZoomIn,
    processZoomOut,
    processReZoom,
    fitViewport,
    initDragging,
    initTouchZoom,
    downloadDiagram,
    cleanup
  }
}
