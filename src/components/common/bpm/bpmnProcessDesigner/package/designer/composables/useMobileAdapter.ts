import { computed, type Ref } from 'vue'

/**
 * 获取屏幕信息
 */
const getScreenInfo = () => {
  return {
    width: window.innerWidth,
    height: window.innerHeight,
    devicePixelRatio: window.devicePixelRatio || 1
  }
}

/**
 * BPMN移动端适配器
 * 负责处理移动端特殊的配置和行为
 */
export function useMobileAdapter(isMobileDevice: Ref<boolean>) {
  
  /**
   * 获取BPMN建模器配置
   * @param container 容器元素
   * @returns 配置对象
   */
  const getBpmnConfig = (container: HTMLElement) => {
    const cfg: any = {
      container,
      bpmnRenderer: {},
      width: '100%',
      height: '100%'
    }

    // 移动端特殊配置
    if (isMobileDevice.value) {
      // 移动端优化的渲染配置
      cfg.bpmnRenderer = {
        defaultFillColor: '#ffffff',
        defaultStrokeColor: '#000000'
      }
    }

    return cfg
  }

  /**
   * 获取画布样式
   * @returns 样式对象
   */
  const getCanvasStyle = computed(() => {
    const baseStyle: any = {
      width: '100%',
      userSelect: 'none'
    }

    if (isMobileDevice.value) {
      return {
        ...baseStyle,
        height: '100%', // 使用父容器高度，不强制100vh
        touchAction: 'none',
        WebkitTouchCallout: 'none',
        WebkitUserSelect: 'none',
        KhtmlUserSelect: 'none',
        MozUserSelect: 'none',
        msUserSelect: 'none'
      }
    } else {
      return {
        ...baseStyle,
        height: '700px',
        touchAction: 'auto'
      }
    }
  })

  /**
   * 获取默认缩放比例
   * @returns 缩放比例
   */
  const getDefaultZoom = () => {
    return isMobileDevice.value ? 0.5 : 1
  }

  /**
   * 获取缩放控制配置
   * @returns 缩放配置
   */
  const getZoomConfig = () => {
    return {
      min: 0.2,
      max: 4,
      step: 0.1,
      // 移动端使用更大的缩放步长
      mobileStep: isMobileDevice.value ? 0.2 : 0.1
    }
  }

  /**
   * 是否显示缩放控制按钮
   * @returns 是否显示
   */
  const shouldShowZoomControls = computed(() => {
    // 移动端不显示缩放控制按钮，使用手势缩放
    return !isMobileDevice.value
  })

  /**
   * 获取覆盖层配置
   * @returns 覆盖层配置
   */
  const getOverlayConfig = () => {
    return {
      // 移动端使用更大的偏移量避免遮挡
      position: {
        left: 0,
        bottom: isMobileDevice.value ? 10 : 0
      },
      // 移动端使用更小的最大宽度
      maxWidth: isMobileDevice.value ? '280px' : '520px',
      minWidth: isMobileDevice.value ? '240px' : '320px'
    }
  }

  /**
   * 获取拖拽配置
   * @returns 拖拽配置
   */
  const getDragConfig = () => {
    return {
      // 移动端使用更大的拖拽阈值
      threshold: isMobileDevice.value ? 10 : 5,
      // 移动端启用惯性滚动
      inertia: isMobileDevice.value,
      // 移动端拖拽灵敏度
      sensitivity: isMobileDevice.value ? 1.2 : 1
    }
  }

  /**
   * 获取触摸事件配置
   * @returns 触摸事件配置
   */
  const getTouchConfig = () => {
    return {
      // 启用触摸事件
      enabled: isMobileDevice.value,
      // 双指缩放配置
      pinchZoom: {
        enabled: isMobileDevice.value,
        minScale: 0.2,
        maxScale: 4,
        sensitivity: 1
      },
      // 单指拖拽配置
      panGesture: {
        enabled: isMobileDevice.value,
        threshold: 10
      },
      // 双击缩放配置
      doubleTap: {
        enabled: isMobileDevice.value,
        zoomFactor: 2
      }
    }
  }

  /**
   * 获取性能优化配置
   * @returns 性能配置
   */
  const getPerformanceConfig = () => {
    return {
      // 移动端启用性能优化
      enableOptimization: isMobileDevice.value,
      // 减少重绘频率
      throttleRedraw: isMobileDevice.value ? 16 : 0, // 60fps
      // 延迟加载非关键元素
      lazyLoad: isMobileDevice.value,
      // 简化渲染
      simplifiedRendering: isMobileDevice.value
    }
  }

  /**
   * 获取事件处理配置
   * @returns 事件配置
   */
  const getEventConfig = () => {
    return {
      // 移动端使用被动事件监听器
      passive: isMobileDevice.value,
      // 事件节流配置
      throttle: {
        scroll: isMobileDevice.value ? 16 : 0,
        resize: isMobileDevice.value ? 100 : 50,
        mousemove: isMobileDevice.value ? 16 : 0
      }
    }
  }

  /**
   * 获取UI适配配置
   * @returns UI配置
   */
  const getUIConfig = () => {
    return {
      // 移动端使用更大的按钮
      buttonSize: isMobileDevice.value ? 'large' : 'small',
      // 移动端使用更大的字体
      fontSize: isMobileDevice.value ? '14px' : '13px',
      // 移动端使用更大的间距
      spacing: isMobileDevice.value ? '12px' : '8px',
      // 移动端图例位置
      legendPosition: isMobileDevice.value ? 'bottom' : 'left'
    }
  }

  /**
   * 获取容器类名
   * @returns 类名数组
   */
  const getContainerClasses = computed(() => {
    const classes = ['my-process-designer']
    
    if (isMobileDevice.value) {
      classes.push('mobile-mode')
    }
    
    return classes
  })

  /**
   * 获取画布容器类名
   * @returns 类名数组
   */
  const getCanvasClasses = computed(() => {
    const classes = ['my-process-designer__canvas']
    
    if (isMobileDevice.value) {
      classes.push('mobile-canvas')
    }
    
    return classes
  })

  /**
   * 处理移动端特殊的初始化逻辑
   * @param bpmnModeler BPMN建模器实例
   */
  const handleMobileInit = (bpmnModeler: any) => {
    if (!isMobileDevice.value || !bpmnModeler) return

    const canvas = bpmnModeler.get('canvas')

    // 移动端默认适应视口，延迟执行确保DOM渲染完成
    setTimeout(() => {
      try {
        // 直接适应视口，让BPMN自动计算最佳缩放
        canvas.zoom('fit-viewport', 'auto')

        // 获取当前缩放比例，如果太大则适当缩小
        const viewbox = canvas.viewbox()
        if (viewbox.scale > 0.8) {
          canvas.zoom(0.6) // 适当缩小以确保在移动端能看到更多内容
        } else if (viewbox.scale < 0.3) {
          canvas.zoom(0.4) // 如果太小则适当放大
        }
      } catch (error) {
        console.debug('Mobile zoom initialization failed:', error)
        // 降级方案：直接设置固定缩放
        canvas.zoom(0.5)
      }
    }, 200)

    // 移动端禁用某些键盘快捷键（安全检查）
    try {
      const keyboard = bpmnModeler.get('keyboard')
      if (keyboard) {
        keyboard.bind(document)
      }
    } catch (error) {
      // BpmnViewer 可能没有 keyboard 服务，忽略错误
      console.debug('Keyboard service not available in BpmnViewer')
    }
  }

  /**
   * 处理移动端视口变化
   * @param bpmnModeler BPMN建模器实例
   */
  const handleViewportChange = (bpmnModeler: any) => {
    if (!isMobileDevice.value || !bpmnModeler) return

    const canvas = bpmnModeler.get('canvas')
    
    // 延迟执行以确保DOM更新完成
    setTimeout(() => {
      canvas.resized()
      canvas.zoom('fit-viewport', 'auto')
    }, 200)
  }

  /**
   * 获取移动端优化的事件监听器选项
   * @param eventType 事件类型
   * @returns 监听器选项
   */
  const getEventListenerOptions = (eventType: string) => {
    if (!isMobileDevice.value) {
      return false
    }

    const passiveEvents = ['touchstart', 'touchmove', 'wheel', 'scroll']
    
    return {
      passive: passiveEvents.includes(eventType),
      capture: false
    }
  }

  return {
    getBpmnConfig,
    getCanvasStyle,
    getDefaultZoom,
    getZoomConfig,
    shouldShowZoomControls,
    getOverlayConfig,
    getDragConfig,
    getTouchConfig,
    getPerformanceConfig,
    getEventConfig,
    getUIConfig,
    getContainerClasses,
    getCanvasClasses,
    handleMobileInit,
    handleViewportChange,
    getEventListenerOptions
  }
}
